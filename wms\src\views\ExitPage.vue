<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 11:56:00
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 11:29:04
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\ExitPage.vue
 * 
-->
<template>
  <div class="exit">
    <el-form ref="ruleFormRef" label-width="100px" :model="addForm" :rules="rules">
      <div style="display: flex;">
        <el-form-item label="货柜" prop="creatorId">
          <el-select v-model="addForm.creatorId" placeholder="请选择" clearable filterable @change="getOutInApiFn">
            <el-option v-for="item in HuoGuiList" :key="item.creatorId" :label="item.cabinetName"
              :value="item.cabinetId" />
          </el-select>
        </el-form-item>
        <el-form-item label="出入口" prop="cabinetGateId">
          <el-select v-model="addForm.cabinetGateId" placeholder="请选择" clearable filterable>
            <el-option v-for="item in OutInList" :key="item.cabinetGateId" :label="item.gateCode"
              :value="item.cabinetGateId" />
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="submitForm" style="margin-left: 10px;">确 定</el-button>
        <el-button @click="cancel">重 置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { getHuoGuoApi, getOutInApi } from '@/api/loginApi'

const router = useRouter();
const { proxy } = getCurrentInstance();

const addForm = ref({});
const ruleFormRef = ref();
const rules = reactive({
  creatorId: [{ required: true, message: '请选择', trigger: 'change' }],
  cabinetGateId: [{ required: true, message: '请选择', trigger: 'change' }],
});

//货柜
let HuoGuiList = ref([])
const getHuoGuoApiFn = async () => {
  HuoGuiList.value = await getHuoGuoApi()
}
getHuoGuoApiFn()

//出入口
let OutInList = ref([])
const getOutInApiFn = async (id) => {
  OutInList.value = await getOutInApi({
    cabinetId: id
  })
}



//确定
const submitForm = () => {
  ruleFormRef.value.validate(valid => {
    if (valid) {

      let obj1 = HuoGuiList.value.find(item => item.creatorId == addForm.value.creatorId)
      if (obj1) {
        localStorage.setItem("huogui.id", obj1.cabinetId)
        localStorage.setItem("huogui.name", obj1.cabinetName)
      }

      let obj2 = OutInList.value.find(item => item.cabinetGateId == addForm.value.cabinetGateId)
      if (obj2) {
        localStorage.setItem("outin.id", obj2.cabinetGateId)
        localStorage.setItem("outin.code", obj2.gateCode)
      }

      router.push({ path: '/login' });
      proxy.$modal.msgSuccess('操作成功');
    }
  });
}

//重置
const cancel = () => {
  addForm.value = {}
  localStorage.removeItem("huogui.id")
  localStorage.removeItem("huogui.name")
  localStorage.removeItem("outin.id")
  localStorage.removeItem("outin.code")
}
</script>

<style lang="scss" scoped>
.exit {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>