<!--
 * @Author: 方志良 
 * @Date: 2024-04-01 16:55:27
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-01 18:37:50
 * @FilePath: \mewyeah-mes-front-end\src\views\system\user\component\UserPassWord.vue
 * 
-->
<template>
  <el-dialog v-model="isvisable" draggable title="重置密码" align-center width="520px" @close="cancelBtn">
    <el-form ref="dialogRef" :model="dialogueForm" label-width="80px" style="padding:30px" :rules="rules">
      <el-form-item label="重置密码" prop="password">
        <el-input v-model="dialogueForm.password" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmBtn">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { resetPasswordApi } from '@/api/system/user'
const { proxy } = getCurrentInstance();
let props = defineProps({
  isvisable: {
    type: Boolean,
    default: false,
  },
  userId: {
    type: Number,
    default: 0,
  }
});
const dialogueForm = ref({})
const rules = reactive({
  password: [{ required: true, message: '此处不能为空', trigger: 'blur' },],
})
const emit = defineEmits(['update:isvisable', 'onSuccess']);
//取消
const dialogRef = ref(null)
const cancelBtn = (e) => {
  dialogueForm.value = {}
  emit('update:isvisable', false);
  dialogRef.value.resetFields()
}
//确定
let loading = ref(false)
const confirmBtn = async () => {
  let flag = await dialogRef.value.validate()
  if (flag) {
    try {
      loading.value = true
      await resetPasswordApi({ userId: props.userId, password: dialogueForm.value.password })
      proxy.$modal.msgSuccess(`重置成功`);
      cancelBtn()
      emit('onSuccess', false);
    } finally {
      loading.value = false
    }

  }
}
</script>

<style lang="scss" ></style>