<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 14:56:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 10:11:00
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Modal\HuoGuiModal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.materialId ? '编辑' : '新增'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
          <el-form-item label="物料名称" prop="materialName">
            <el-input v-model="addForm.materialName" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="addForm.materialCode" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="物料规格" prop="materialSpec">
            <el-input v-model="addForm.materialSpec" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              type="textarea"
              maxlength="200"
              :rows="2"
              placeholder="请输入"
              v-model="addForm.remark"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading">
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { addMaterialPageApi, editMaterialPageApi } from "@/api/home/<USER>";
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  materialName: [{ required: true, message: "请输入", trigger: "blur" }],
  materialCode: [{ required: true, message: "请输入", trigger: "blur" }],
  materialSpec: [{ required: true, message: "请输入", trigger: "blur" }],
});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      if (addForm.value.materialId) {
        requestObj = editMaterialPageApi(addForm.value);
      } else {
        requestObj = addMaterialPageApi(addForm.value);
      }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
