<!--
 * @Author: 方志良 
 * @Date: 2024-04-03 09:46:11
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-08 11:10:31
 * @FilePath: \mewyeah-mes-front-end\src\views\system\role\component\RoleAddOrEdit.vue
 * 
-->

<template>
  <el-dialog v-model="isvisable" draggable :title="editObj.roleId ? '编辑' : '新增'" align-center width="520px"
    @close="cancelBtn">
    <el-form ref="dialogRef" :model="dialogueForm" label-width="80px" style="padding:30px" :rules="rules">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="dialogueForm.roleName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea"  maxlength="200"   :rows="2" placeholder="请输入" v-model="dialogueForm.remark"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmBtn">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { addRole, updateRole } from '@/api/system/roleApi'
const { proxy } = getCurrentInstance();
let props = defineProps({
  isvisable: {
    type: Boolean,
    default: false,
  },
  editObj: {
    type: Object,
    default: () => { },
  }
});

const dialogueForm = ref({})
const rules = reactive({
  roleName: [{ required: true, message: '此处不能为空', trigger: 'blur' },],
})

watchEffect(() => {
  if (props.isvisable && Object.keys(props.editObj).length > 0) {
    dialogueForm.value = JSON.parse(JSON.stringify(props.editObj));
  }
});


const emit = defineEmits(['update:isvisable', 'onSuccess']);
//取消
const dialogRef = ref(null)
const cancelBtn = () => {
  dialogueForm.value = {}
  emit('update:isvisable', false);
  dialogRef.value.resetFields()
}

//确定
let loading = ref(false)
const confirmBtn = async () => {
  let flag = await dialogRef.value.validate()
  if (flag) {
    try {
      loading.value = true
      if (dialogueForm.value.roleId) {
        await updateRole(dialogueForm.value)
        proxy.$modal.msgSuccess(`编辑成功`);
        cancelBtn()
        emit('onSuccess');
      } else {
        await addRole(dialogueForm.value)
        proxy.$modal.msgSuccess(`新增成功`);
        cancelBtn()
        emit('onSuccess');
      }
    } finally {
      loading.value = false
    }
  }
}


onMounted(() => {
})
</script>

<style lang="scss" ></style>