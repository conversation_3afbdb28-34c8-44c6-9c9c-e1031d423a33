<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 14:56:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 10:11:00
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Modal\HuoGuiModal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.orderId ? '编辑' : '新增'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
          <el-form-item label="外部识别" prop="externalMarkCode">
            <el-input v-model="addForm.externalMarkCode" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="客户" prop="customer">
            <el-input v-model="addForm.customer" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="订单类型" prop="orderTypeStatusValue">
            <el-radio-group v-model="addForm.orderTypeStatusValue">
              <el-radio :label="1">入库</el-radio>
              <el-radio :label="2">出库</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="目标出入口" prop="gateCode">
            <el-select
              v-model="addForm.gateCode"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%;"
            >
              <el-option
                v-for="item in OutInList"
                :key="item.cabinetGateId"
                :label="item.gateCode"
                :value="item.gateCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="订单优先级" prop="priorityStatusValue">
            <el-radio-group v-model="addForm.priorityStatusValue">
              <el-radio :label="1">高</el-radio>
              <el-radio :label="2">低</el-radio>
              <el-radio :label="3">中</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              type="textarea"
              maxlength="200"
              :rows="2"
              placeholder="请输入"
              v-model="addForm.remark"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading">
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { addOrderPageApi, editOrderPageApi } from "@/api/home/<USER>";
import { getOutInApi } from "@/api/loginApi";
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  externalMarkCode: [{ required: true, message: "请输入", trigger: "blur" }],
  customer: [{ required: true, message: "请输入", trigger: "blur" }],
  orderTypeStatusValue: [{ required: true, message: "请选择", trigger: "blur" }],
  gateCode: [{ required: true, message: "请选择", trigger: "blur" }],
  priorityStatusValue: [{ required: true, message: "请选择", trigger: "blur" }],
});

//出入口
let OutInList = ref([]);
const getOutInApiFn = async () => {
  OutInList.value = await getOutInApi({
    cabinetId: localStorage.getItem("huogui.id"),
  });
};

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");

  getOutInApiFn();
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      if (addForm.value.orderId) {
        requestObj = editOrderPageApi({
          ...addForm.value,
          cabinetId: localStorage.getItem("huogui.id"),
        });
      } else {
        requestObj = addOrderPageApi({
          ...addForm.value,
          cabinetId: localStorage.getItem("huogui.id"),
        });
      }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
