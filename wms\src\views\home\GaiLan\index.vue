<template>
  <div class="oc" style="background-color: #fff">
    <el-tabs v-model="activeName" :stretch="true" type="card" @tab-click="handleClick">
      <el-tab-pane label="未发布" :name="1"></el-tab-pane>
      <el-tab-pane label="已发布" :name="2"></el-tab-pane>
      <el-tab-pane label="已完成" :name="3"></el-tab-pane>
      <el-tab-pane label="异常" :name="4"></el-tab-pane>
    </el-tabs>
    <div class="action">
      <div>
        <el-button type="primary" @click="handleAddEdit({})">添加</el-button>
      </div>
      <div class="selection">
        <SearchList
          v-model="searchForms.keywords"
          @update:modelValue="theResetPageFn"
        ></SearchList>
      </div>
    </div>

    <el-table :data="tableList">
      <el-table-column
        label="外部识别"
        prop="externalMarkCode"
        align="center"
      ></el-table-column>
      <el-table-column label="客户" prop="customer" align="center"></el-table-column>
      <el-table-column
        label="订单类型"
        prop="orderTypeStatusLabel"
        align="center"
      ></el-table-column>
      <el-table-column
        label="目标出入口"
        prop="gateCode"
        align="center"
      ></el-table-column>
      <el-table-column
        label="订单优先级"
        prop="priorityStatusLabel"
        align="center"
      ></el-table-column>
      <el-table-column label="备注" prop="remark" align="center"></el-table-column>

      <el-table-column label="操作" width="200" align="center">
        <template #default="scope">
          <div class="div-flex">
            <el-button class="table-btn" text @click="projectBtn(scope.row)">
              项目
            </el-button>
            <el-button
              class="table-btn"
              v-if="activeName == 1"
              text
              @click="issueBtn(scope.row)"
            >
              发布
            </el-button>
            <el-button
              class="table-btn"
              text
              v-if="activeName == 2"
              @click="resetBtn(scope.row)"
            >
              撤销
            </el-button>
            <el-button class="table-btn" text @click="handleAddEdit(scope.row)">
              编辑
            </el-button>
            <el-button class="table-btn" text @click="deleteBtn(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row justify="end" style="margin-top: 10px">
      <el-pagination
        v-show="thePage.total > 0"
        background
        :total="thePage.total"
        v-model:current-page="thePage.current"
        v-model:page-size="thePage.size"
        @current-change="thePageFn"
        layout="total,prev, pager, next"
      ></el-pagination>
    </el-row>
    <AddOrEditModal ref="addEdieModalRef" @on-success="theResetPageFn"></AddOrEditModal>
    <Project ref="ProjectRef" @on-success="theResetPageFn"></Project>
  </div>
</template>

<script setup>
import {
  getOrderPageApi,
  deleteOrderPageApi,
  issueOrderPageApi,
  resetOrderPageApi,
} from "@/api/home/<USER>";
const AddOrEditModal = defineAsyncComponent(() =>
  import("@/views/home/<USER>/modal/AddOrEditModal.vue")
);
const Project = defineAsyncComponent(() => import("./project.vue"));
const { proxy } = getCurrentInstance();

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0,
});
const tableList = ref([]);
const thePageFn = async () => {
  let obj = {
    cabinetId: localStorage.getItem("huogui.id"),
    orderStatusValue: activeName.value,
    pageNum: thePage.value.current,
    pageSize: thePage.value.size,
    keywords: searchForms.value.keywords,
  };
  let res = await getOrderPageApi(obj);
  tableList.value = res.rows;
  thePage.value.total = res.total;
};

//搜索
const searchForms = ref({
  keywords: "",
});
const theResetPageFn = () => {
  thePage.value.current = 1;
  thePageFn();
};

//编辑
const addEdieModalRef = ref(null);
const handleAddEdit = (row) => {
  addEdieModalRef.value.openModal(row);
};

//删除
const deleteBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认删除此数据项？`)
    .then(() => deleteOrderPageApi(row.orderId))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn();
    })
    .catch(() => {});
};

//发布
const issueBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认发布此数据项？`)
    .then(() => issueOrderPageApi(row.orderId))
    .then(() => {
      proxy.$modal.msgSuccess(`发布成功`);
      thePageFn();
    })
    .catch(() => {});
};

//撤销
const resetBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认撤销发布此数据项？`)
    .then(() => resetOrderPageApi(row.orderId))
    .then(() => {
      proxy.$modal.msgSuccess(`撤销成功`);
      thePageFn();
    })
    .catch(() => {});
};

//订单
const ProjectRef = ref(null);
const projectBtn = (row) => {
  ProjectRef.value.openModal(row);
};

//tab栏
let activeName = ref(1);
const handleClick = () => {
  searchForms.value.status = "全部";
  nextTick(() => {
    thePageFn();
  });
};

onMounted(() => {
  thePageFn();
});
</script>

<style lang="scss" scoped></style>
