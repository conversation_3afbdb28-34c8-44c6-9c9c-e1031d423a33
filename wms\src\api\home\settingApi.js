import request from '@/utils/request';

//货品分页
export const getMaterialPageApi = (params) => {
  return request({
    url: `/factory/material/table`,
    method: 'get',
    params
  });
}

//货品新增
export const addMaterialPageApi = (data) => {
  return request({
    url: '/factory/material/add',
    method: 'post',
    data
  });
}

//货品编辑
export const editMaterialPageApi = (data) => {
  return request({
    url: '/factory/material/update',
    method: 'put',
    data
  });
}

//货品删除
export const deleteMaterialPageApi = (id) => {
  return request({
    url: `/factory/material/del/${id}`,
    method: 'delete',
  });
}

//箱型分页
export const getBoxPageApi = (params) => {
  return request({
    url: `/factory/box/table`,
    method: 'get',
    params
  });
}

//箱型新增
export const addBoxPageApi = (data) => {
  return request({
    url: '/factory/box/add',
    method: 'post',
    data
  });
}

//箱型编辑
export const editBoxPageApi = (data) => {
  return request({
    url: '/factory/box/update',
    method: 'put',
    data
  });
}

//箱型删除
export const deleteBoxPageApi = (id) => {
  return request({
    url: `/factory/box/del/${id}`,
    method: 'delete',
  });
}









//货柜分页
export const getHuoGuiDataApi = (params) => {
  return request({
    url: `/factory/cabinet/table`,
    method: 'get',
    params
  });
}


//新增货柜
export const addHuoGuiDataApi = (data) => {
  return request({
    url: `/factory/cabinet/add`,
    method: 'post',
    data
  });
}


//更新货柜
export const updateHuoGuiDataApi = (data) => {
  return request({
    url: '/factory/cabinet/update',
    method: 'put',
    data
  });
}

//删除货柜
export const deleteHuoGuiDataApi = (id) => {
  return request({
    url: `/factory/cabinet/del/${id}`,
    method: 'delete',
  });
}




//出入口分页
export const getOutInPageApi = (params) => {
  return request({
    url: `/factory/cabinet/gate/table`,
    method: 'get',
    params
  });
}

//出入口新增
export const addOutInPageApi = (data) => {
  return request({
    url: '/factory/cabinet/gate/add',
    method: 'post',
    data
  });
}

//出入口编辑
export const editOutInPageApi = (data) => {
  return request({
    url: '/factory/cabinet/gate/update',
    method: 'put',
    data
  });
}

//出入口删除
export const deleteOutInPageApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/del/${id}`,
    method: 'delete',
  });
}

//出入口启停
export const adjustOutInStatusApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/switch/${id}`,
    method: 'put',
  })
}

//安全门启停
export const adjustDoorStatusApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/door/switch/${id}`,
    method: 'put',
  })
}

//激光指示器启停
export const adjustExitStatusApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/pointer/switch/${id}`,
    method: 'put',
  })
}



//获取托盘列表
export const getTrayListApi = (params) => {
  return request({
    url: `/factory/tray/list`,
    method: 'get',
    params
  });
}

//获取箱型列表
export const getBoxTypeListApi = (params) => {
  return request({
    url: `/factory/box/list`,
    method: 'get',
    params
  });
}


//获取库位
export const getLocationListApi = (params) => {
  return request({
    url: `/wh/location/list`,
    method: 'get',
    params
  });
}

//保存库位
export const saveLocationListApi = (data) => {
  return request({
    url: `/wh/location/save`,
    method: 'post',
    data
  });
}

//删除库位
export const deleteLocationListApi = (id) => {
  return request({
    url: `/wh/location/del/${id}`,
    method: 'delete',
  });
}

//生成单个库位名称
export const createNameLocationApi = (params) => {
  return request({
    url: `/wh/location/create/local/name`,
    method: 'get',
    params
  });
}

//生成批量库位名称
export const batchNameLocationApi = (params) => {
  return request({
    url: `/wh/location/create/local/name/batch`,
    method: 'get',
    params
  });
}

