<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 17:07:42
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 14:32:54
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\index.vue
 * 
-->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" :stretch="true" type="card">
      <el-tab-pane label="货品" :name="1"></el-tab-pane>
      <el-tab-pane label="箱型" :name="2"></el-tab-pane>
      <el-tab-pane label="出入口" :name="3"></el-tab-pane>
      <el-tab-pane label="货柜" :name="4"></el-tab-pane>
    </el-tabs>

    <div style="display: flex; justify-content: center">
      <Material v-if="activeName == 1"></Material>
      <Box v-if="activeName == 2"></Box>
      <Outin v-if="activeName == 3"></Outin>
      <Cabinet v-if="activeName == 4"></Cabinet>
    </div>
  </div>
</template>

<script setup>
const Material = defineAsyncComponent(() => import("./Material.vue"));
const Box = defineAsyncComponent(() => import("./Box.vue"));
const Outin = defineAsyncComponent(() => import("./Outin.vue"));
const Cabinet = defineAsyncComponent(() => import("./Cabinet.vue"));

let activeName = ref(1);

onMounted(() => {});
</script>

<style lang="scss" scoped>
.el-tabs {
  width: 40%;
  margin: 0 auto;
}
</style>
