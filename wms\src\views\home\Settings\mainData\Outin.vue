<template>
  <div class="oc" style="background-color: #fff">
    <div class="action">
      <div>
        <el-button type="primary" @click="handleAddEdit({})">添加</el-button>
      </div>
      <div class="selection">
        <SearchList
          v-model="searchForms.keywords"
          @update:modelValue="theResetPageFn"
        ></SearchList>
      </div>
    </div>

    <el-table :data="tableList">
      <el-table-column label="编号" prop="gateCode" align="center"></el-table-column>
      <el-table-column label="方向" prop="gateDirection" align="center"></el-table-column>
      <el-table-column label="支架高度mm" prop="gateRackHeight" align="center">
      </el-table-column>
      <el-table-column label="偏移mm" prop="gateOffset" align="center"> </el-table-column>
      <el-table-column label="高度mm" prop="gateHeight" align="center"> </el-table-column>

      <el-table-column
        label="激光指示器状态"
        prop="laserPointerEnableStatusValue"
        align="center"
      >
        <template #default="scope">
          <el-switch
            v-model="scope.row.laserPointerEnableStatusValue"
            inline-prompt
            active-value="1"
            inactive-value="0"
            active-text="启用"
            inactive-text="停用"
            @change="laserPointerEnableStatusValueBtn(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="安全门状态" prop="doorEnableStatusValue" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.doorEnableStatusValue"
            inline-prompt
            active-value="1"
            inactive-value="0"
            active-text="启用"
            inactive-text="停用"
            @change="doorEnableStatusValueBtn(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="出入口状态" prop="statusValue" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.statusValue"
            inline-prompt
            active-value="1"
            inactive-value="0"
            active-text="启用"
            inactive-text="停用"
            @change="statusValueBtn(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" align="center">
        <template #default="scope">
          <div class="div-flex">
            <el-button class="table-btn" text @click="handleAddEdit(scope.row)"
              >编辑</el-button
            >
            <el-button class="table-btn" text @click="deleteBtn(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row justify="end" style="margin-top: 10px">
      <el-pagination
        v-show="thePage.total > 0"
        background
        :total="thePage.total"
        v-model:current-page="thePage.current"
        v-model:page-size="thePage.size"
        @current-change="thePageFn"
        layout="total,prev, pager, next"
      >
      </el-pagination>
    </el-row>

    <OutinModal ref="addEdieModalRef" @on-success="theResetPageFn"></OutinModal>
  </div>
</template>

<script setup>
import {
  getOutInPageApi,
  deleteOutInPageApi,
  adjustOutInStatusApi,
  adjustDoorStatusApi,
  adjustExitStatusApi,
} from "@/api/home/<USER>";
const OutinModal = defineAsyncComponent(() => import("./Modal/OutinModal.vue"));
const { proxy } = getCurrentInstance();

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0,
});
const tableList = ref([]);
const thePageFn = async () => {
  let obj = {
    cabinetId: localStorage.getItem("huogui.id"),
    pageNum: thePage.value.current,
    pageSize: thePage.value.size,
    keywords: searchForms.value.keywords,
  };
  let res = await getOutInPageApi(obj);
  tableList.value = res.rows;
  thePage.value.total = res.total;
};

//搜索
const searchForms = ref({
  keywords: "",
});
const theResetPageFn = () => {
  thePage.value.current = 1;
  thePageFn();
};

//编辑
const addEdieModalRef = ref(null);
const handleAddEdit = (row) => {
  addEdieModalRef.value.openModal(row);
};

//删除
const deleteBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认删除此数据项？`)
    .then(() => deleteOutInPageApi(row.cabinetGateId))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn();
    })
    .catch(() => {});
};

//出入口
const statusValueBtn = (row) => {
  const text = row.statusValue === "1" ? "启用" : "停用";
  proxy.$modal
    .confirm(`是否确认${text}？`)
    .then(() => adjustOutInStatusApi(row.cabinetGateId))
    .then(() => {
      proxy.$modal.msgSuccess(`操作成功`);
      thePageFn();
    })
    .catch(() => {
      row.statusValue = row.statusValue === "0" ? "1" : "0";
    });
};

//激光指示器
const laserPointerEnableStatusValueBtn = (row) => {
  const text = row.laserPointerEnableStatusValue === "1" ? "启用" : "停用";
  proxy.$modal
    .confirm(`是否确认${text}？`)
    .then(() => adjustExitStatusApi(row.cabinetGateId))
    .then(() => {
      proxy.$modal.msgSuccess(`操作成功`);
      thePageFn();
    })
    .catch(() => {
      row.laserPointerEnableStatusValue =
        row.laserPointerEnableStatusValue === "0" ? "1" : "0";
    });
};

//安全门
const doorEnableStatusValueBtn = (row) => {
  const text = row.doorEnableStatusValue === "1" ? "启用" : "停用";
  proxy.$modal
    .confirm(`是否确认${text}？`)
    .then(() => adjustDoorStatusApi(row.cabinetGateId))
    .then(() => {
      proxy.$modal.msgSuccess(`操作成功`);
      thePageFn();
    })
    .catch(() => {
      row.doorEnableStatusValue = row.doorEnableStatusValue === "0" ? "1" : "0";
    });
};

onMounted(() => {
  thePageFn();
});
</script>

<style lang="scss" scoped></style>
