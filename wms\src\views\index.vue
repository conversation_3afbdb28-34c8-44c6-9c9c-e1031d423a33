<!--
 * @Author: 方志良 
 * @Date: 2024-04-08 15:53:26
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-08 16:24:04
 * @FilePath: \biweiman-mes-wms-front-end\src\views\index.vue
 * 
-->
<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="24"> </el-col>
    </el-row>
    <!-- <el-divider /> -->
    <div class="myInfo">
      <img :src="userAvatar" alt="图片不存在">
      <div class="myInfo-name">
        <div class='myInfo-name-name'>{{ userName }}</div>
        <div class="myInfo-name-position">{{ myPosition }}</div>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">


import { useStore } from 'vuex'
const store = useStore()
const userAvatar = computed(() => {
  return store.getters.avatar || '/src/assets/images/logo.png';
});
const userName = computed(() => {
  return store.getters.name || '用户';
});
const myPosition = computed(() => {
  return store.state.user.userinfo?.postName || '管理员';
});
onMounted(() => {

})
</script>

<style scoped lang="scss">
.home {
  .myInfo {
    display: flex;

    img {
      width: 150px;
      height: 150px;
    }

    &-name {

      margin-left: 20px;

      &-name {
        margin-top: 20px;
        font-weight: bold;
        font-size: 30px;
        color: #515a6e;
      }

      &-position {

        margin-top: 30px;
        font-weight: 700;
        font-size: 16px;
        color: #006699;
        border: 1px solid #006699;
        text-align: center;
        border-radius: 5px;
      }
    }
  }
}
</style>
