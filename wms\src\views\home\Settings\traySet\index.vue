<template>
  <div class="app-coniner">
    <div class="tray-layout">
      <div class="tray-select">
        <div class="tray-title">托盘</div>
        <el-form label-width="80px" :model="addForm" style="margin-bottom: 30px">
          <el-form-item label="托盘:" prop="lineId">
            <el-select
              v-model="addForm.lineId"
              placeholder="请选择"
              clearable
              filterable
              @change="trayChange"
              style="width: 300px"
            >
              <el-option
                v-for="item in trayList"
                :key="item.trayId"
                :label="item.trayCode"
                :value="item.trayCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="托盘名称:" prop="lineId">
            {{ selectedTray ? selectedTray.trayName : "" }}
          </el-form-item>
        </el-form>

        <div class="tray-title">功能区</div>
        <div class="tray-btn">
          <div>
            <el-button
              type="primary"
              @click="adjustBtn"
              style="margin: 10px"
              :disabled="selectedBoxIndex === -1"
            >
              排列方式
            </el-button>
            <span class="drag-tip">💡 提示：点击可横向纵向排列</span>
          </div>

          <div>
            <el-button
              type="danger"
              style="margin: 10px"
              @click="clearOneBtn"
              :disabled="selectedBoxIndex === -1"
            >
              删除
            </el-button>
            <span class="drag-tip">💡 提示：选择一个箱子，可点击删除</span>
          </div>

          <div>
            <el-button type="warning" style="margin: 10px" @click="clearAllBtn">
              清空
            </el-button>
            <span class="drag-tip">💡 提示：清空托盘上的箱子</span>
          </div>

          <div>
            <el-button type="success" style="margin: 10px" @click="saveBtn">
              保存
            </el-button>
            <span class="drag-tip">💡 提示：保存当前托盘的箱子数据</span>
          </div>

          <div class="drag-tip">💡 提示：长按箱子0.5秒可开启拖拽模式</div>
        </div>
      </div>
      <div class="container" ref="containerRef">
        <!-- 托盘宽度标签 -->
        <div v-if="selectedTray" class="width-label">
          长度: {{ selectedTray.trayLength }}mm
        </div>

        <!-- 托盘高度标签 -->
        <div v-if="selectedTray" class="height-label">
          宽度: {{ selectedTray.trayWidth }}mm
        </div>

        <!-- 托盘 -->
        <div
          v-if="selectedTray"
          class="tray"
          :style="{
            width: '1200px',
            height: '600px',
          }"
        >
          <!-- 刻度线 -->
          <div class="scale-lines">
            <!-- 垂直刻度线 -->
            <div
              v-for="i in Math.floor(selectedTray.trayLength / 50)"
              :key="'v-' + i"
              class="scale-line vertical"
              :style="{ left: i * 50 * scaleX + 'px' }"
            ></div>
            <!-- 水平刻度线 -->
            <div
              v-for="i in Math.floor(selectedTray.trayWidth / 50)"
              :key="'h-' + i"
              class="scale-line horizontal"
              :style="{ top: i * 50 * scaleY + 'px' }"
            ></div>
          </div>

          <!-- 箱子 -->
          <div
            v-for="(box, index) in placedBoxes"
            :key="index"
            class="box"
            :class="{
              selected: selectedBoxIndex === index,
              dragging: isDragging && dragBoxIndex === index,
            }"
            :style="{
              left: box.locationX * scaleX + 'px',
              bottom: box.locationY * scaleY + 'px',
              width: box.boxLength * scaleX + 'px',
              height: box.boxWidth * scaleY + 'px',
              zIndex: isDragging && dragBoxIndex === index ? 1000 : 1,
            }"
            @click="selectBox(index)"
            @mousedown="startLongPress($event, index)"
            @touchstart="startLongPress($event, index)"
            @mouseenter="showBoxPosition($event, index)"
            @mousemove="updateMousePosition($event, index)"
            @mouseleave="hideBoxPosition"
          >
            {{ box.locationName }}

            <!-- 位置信息提示 -->
            <div
              v-if="hoveredBoxIndex === index && !isDragging"
              class="position-tooltip"
              :style="{
                left: mousePosition.x + 'px',
                top: mousePosition.y + 'px',
              }"
            >
              x: {{ Math.round(box.locationX) }} y: {{ Math.round(box.locationY) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-fun">
      <!-- 箱子 -->
      <div class="box-type">
        <div class="tray-title">箱型</div>
        <div class="oc">
          <div class="action">
            <div></div>
            <div class="selection">
              <SearchList
                v-model="searchForms1.keywords"
                @update:modelValue="getBoxTypeListApiFn"
              ></SearchList>
            </div>
          </div>

          <el-table :data="boxList">
            <el-table-column
              label="型号"
              prop="boxTypeName"
              align="center"
            ></el-table-column>
            <el-table-column label="长度" prop="length" align="center"></el-table-column>
            <el-table-column label="宽度" prop="width" align="center"></el-table-column>
            <el-table-column label="高度" prop="height" align="center"></el-table-column>
            <el-table-column label="操作" width="160" align="center">
              <template #default="scope">
                <el-button text type="success" @click="pullAllBtn(scope.row)">
                  铺满
                </el-button>
                <el-button text type="success" @click="pullOneBtn(scope.row)">
                  单个
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 库位 -->
      <div class="kuwei">
        <div class="tray-title">库位</div>
        <div class="oc">
          <div class="action">
            <div></div>
            <div class="selection">
              <SearchList
                v-model="searchForms2.keywords"
                @update:modelValue="getLocationListApiFn"
              ></SearchList>
            </div>
          </div>
          <el-table :data="kuweiList">
            <el-table-column
              label="名称"
              prop="locationName"
              align="center"
            ></el-table-column>
            <el-table-column
              label="长度"
              prop="boxLength"
              align="center"
            ></el-table-column>
            <el-table-column
              label="宽度"
              prop="boxWidth"
              align="center"
            ></el-table-column>
            <el-table-column
              label="x坐标"
              prop="locationX"
              align="center"
            ></el-table-column>
            <el-table-column
              label="y坐标"
              prop="locationY"
              align="center"
            ></el-table-column>
            <el-table-column
              label="箱型"
              prop="boxTypeName"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import {
  getTrayListApi,
  getBoxTypeListApi,
  getLocationListApi,
  saveLocationListApi,
  deleteLocationListApi,
  createNameLocationApi,
  batchNameLocationApi,
} from "@/api/home/<USER>";
const addForm = ref({});
const containerRef = ref(null);
const selectedTray = ref(null);
const placedBoxes = ref([]);
const selectedBoxIndex = ref(-1);

// 拖拽相关状态
const isDragging = ref(false);
const dragStartTime = ref(0);
const longPressTimer = ref(null);
const dragOffset = ref({ locationX: 0, locationY: 0 });
const dragBoxIndex = ref(-1);
const originalPosition = ref({ locationX: 0, locationY: 0 }); // 记录拖拽前的原始位置

// 鼠标悬停显示位置信息
const hoveredBoxIndex = ref(-1);
const mousePosition = ref({ x: 0, y: 0 });

// 计算缩放比例
const scaleX = computed(() => {
  return selectedTray.value ? 1200 / selectedTray.value.trayLength : 1;
});

const scaleY = computed(() => {
  return selectedTray.value ? 600 / selectedTray.value.trayWidth : 1;
});

const trayList = ref([]);
//获取托盘
const getTrayListFn = async () => {
  trayList.value = await getTrayListApi({
    cabinetId: localStorage.getItem("huogui.id"),
    statusValue: "1",
  });
};
getTrayListFn();

//获取库位
let kuweiList = ref([]);
let searchForms2 = ref({});
const getLocationListApiFn = async () => {
  if (!selectedTray.value) return;
  kuweiList.value = await getLocationListApi({
    cabinetId: localStorage.getItem("huogui.id"),
    trayId: selectedTray.value.trayId,
    keywords: searchForms2.value.keywords,
  });

  if (kuweiList.value && kuweiList.value.length > 0) {
    placedBoxes.value = kuweiList.value.map((item) => {
      return {
        locationId: item.locationId,
        locationName: item.locationName,
        boxLength: item.boxLength,
        boxWidth: item.boxWidth,
        locationX: item.locationX,
        locationY: item.locationY,
        rotated: item.boxDirection == 2 ? true : false,
        boxTypeName: item.boxTypeName,
        boxHeight: item.boxHeight,
      };
    });
  } else {
    placedBoxes.value = []; // 清空已放置的箱子
  }
};
const trayChange = (val) => {
  selectedTray.value = trayList.value.find((item) => item.trayCode === val);
  selectedBoxIndex.value = -1; // 清空选择的箱子

  getLocationListApiFn();
};

//获取箱型
let boxList = ref([]);
let searchForms1 = ref({});
let getBoxTypeListApiFn = async () => {
  boxList.value = await getBoxTypeListApi({
    keywords: searchForms1.value.keywords,
  });
};
getBoxTypeListApiFn();

//单个
const pullOneBtn = async (row) => {
  if (!selectedTray.value) {
    ElMessage.warning("请先选择托盘");
    return;
  }
  let res = await createNameLocationApi({
    cabinetId: localStorage.getItem("huogui.id"),
    trayCode: selectedTray.value.trayCode,
  });
  const newBox = {
    locationName: res.locationName,
    boxLength: row.length,
    boxWidth: row.width,
    locationX: 0,
    locationY: 0,
    rotated: false, // 添加旋转状态
    boxTypeName: row.boxTypeName,
    boxHeight: row.height,
  };

  // 计算放置位置
  const position = findAvailablePosition(newBox);
  if (position) {
    newBox.locationX = position.x;
    newBox.locationY = position.y;
    placedBoxes.value.push(newBox);
  } else {
    ElMessage.warning("托盘空间不足");
  }
};

const pullAllBtn = async (row) => {
  if (!selectedTray.value) {
    ElMessage.warning("请先选择托盘");
    return;
  }

  // 计算能放多少个箱子
  const maxCols = Math.floor(selectedTray.value.trayLength / row.length);
  const maxRows = Math.floor(selectedTray.value.trayWidth / row.width);
  const totalBoxes = maxCols * maxRows;

  let res = await batchNameLocationApi({
    cabinetId: localStorage.getItem("huogui.id"),
    trayCode: selectedTray.value.trayCode,
    qty: totalBoxes,
  });
  if (res.length == totalBoxes) {
    // 清空当前箱子，重新铺满
    placedBoxes.value = [];
    selectedBoxIndex.value = -1;

    for (let i = 0; i < totalBoxes; i++) {
      const col = i % maxCols;
      const rowIndex = Math.floor(i / maxCols);
      placedBoxes.value.push({
        locationName: res[i] ? res[i].locationName : "",
        boxLength: row.length,
        boxWidth: row.width,
        locationX: col * row.length,
        locationY: rowIndex * row.width,
        rotated: false,
        boxTypeName: row.boxTypeName,
        boxHeight: row.height,
      });
    }
  } else {
    ElMessage.warning("接口返回数据错误,请联系技术人员");
  }
};

// 检查两个矩形是否重叠
const isOverlapping = (box1, box2) => {
  return !(
    box1.locationX + box1.boxLength <= box2.locationX ||
    box2.locationX + box2.boxLength <= box1.locationX ||
    box1.locationY + box1.boxWidth <= box2.locationY ||
    box2.locationY + box2.boxWidth <= box1.locationY
  );
};

// 检查位置是否可用
const isPositionAvailable = (newBox, x, y) => {
  const testBox = { ...newBox, locationX: x, locationY: y };

  // 检查是否超出托盘范围
  if (
    x < 0 ||
    y < 0 ||
    x + newBox.boxLength > selectedTray.value.trayLength ||
    y + newBox.boxWidth > selectedTray.value.trayWidth
  ) {
    return false;
  }

  // 检查是否与现有箱子重叠
  for (const existingBox of placedBoxes.value) {
    if (isOverlapping(testBox, existingBox)) {
      return false;
    }
  }

  return true;
};

// 寻找可用位置
const findAvailablePosition = (newBox) => {
  // 从左下角开始，逐行扫描
  for (let y = 0; y <= selectedTray.value.trayWidth - newBox.boxWidth; y += 10) {
    for (let x = 0; x <= selectedTray.value.trayLength - newBox.boxLength; x += 10) {
      if (isPositionAvailable(newBox, x, y)) {
        return { x, y };
      }
    }
  }
  return null;
};

// 选择箱子
const selectBox = (index) => {
  if (!isDragging.value) {
    selectedBoxIndex.value = index;
  }
};

// 开始长按检测
const startLongPress = (event, index) => {
  event.preventDefault();

  // 清除之前的定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
  }

  dragStartTime.value = Date.now();

  // 设置长按定时器（0.5秒）
  longPressTimer.value = setTimeout(() => {
    startDrag(event, index);
  }, 500);

  // 添加鼠标抬起和触摸结束事件监听
  const cleanup = () => {
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
    document.removeEventListener("mouseup", cleanup);
    document.removeEventListener("touchend", cleanup);
  };

  document.addEventListener("mouseup", cleanup);
  document.addEventListener("touchend", cleanup);
};

// 开始拖拽
const startDrag = (event, index) => {
  isDragging.value = true;
  dragBoxIndex.value = index;
  selectedBoxIndex.value = index;

  const box = placedBoxes.value[index];

  // 记录原始位置
  originalPosition.value = {
    locationX: box.locationX,
    locationY: box.locationY,
  };

  const rect = containerRef.value.getBoundingClientRect();

  // 获取鼠标或触摸位置
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  // 计算鼠标相对于箱子的偏移
  dragOffset.value = {
    locationX: clientX - rect.left - box.locationX * scaleX.value,
    locationY: rect.bottom - clientY - box.locationY * scaleY.value,
  };

  // 添加拖拽事件监听
  document.addEventListener("mousemove", handleDrag);
  document.addEventListener("touchmove", handleDrag);
  document.addEventListener("mouseup", endDrag);
  document.addEventListener("touchend", endDrag);

  ElMessage.info("开始拖拽模式");
};

// 处理拖拽移动
const handleDrag = (event) => {
  if (!isDragging.value || dragBoxIndex.value === -1) return;

  event.preventDefault();

  const rect = containerRef.value.getBoundingClientRect();
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  // 计算新位置（实际坐标）
  const newX = (clientX - rect.left - dragOffset.value.locationX) / scaleX.value;
  const newY = (rect.bottom - clientY - dragOffset.value.locationY) / scaleY.value;

  const box = placedBoxes.value[dragBoxIndex.value];

  // 边界检查
  const clampedX = Math.max(0, Math.min(newX, selectedTray.value.trayLength - box.boxLength));
  const clampedY = Math.max(0, Math.min(newY, selectedTray.value.trayWidth - box.boxWidth));

  // 更新箱子位置
  placedBoxes.value[dragBoxIndex.value] = {
    ...box,
    locationX: clampedX,
    locationY: clampedY,
  };
};

// 结束拖拽
const endDrag = (event) => {
  if (!isDragging.value || dragBoxIndex.value === -1) return;

  event.preventDefault();

  const box = placedBoxes.value[dragBoxIndex.value];

  // 检查最终位置是否与其他箱子重叠
  const otherBoxes = placedBoxes.value.filter((_, index) => index !== dragBoxIndex.value);
  let hasOverlap = false;

  for (const otherBox of otherBoxes) {
    if (isOverlapping(box, otherBox)) {
      hasOverlap = true;
      break;
    }
  }

  if (hasOverlap) {
    // 如果重叠，恢复到拖拽前的原始位置
    placedBoxes.value[dragBoxIndex.value] = {
      ...box,
      locationX: originalPosition.value.locationX,
      locationY: originalPosition.value.locationY,
    };
    ElMessage.warning("位置重叠，已恢复到原位置");
  } else {
    ElMessage.success("拖拽完成");
  }

  // 清理拖拽状态
  isDragging.value = false;
  dragBoxIndex.value = -1;
  dragOffset.value = { locationX: 0, locationY: 0 };

  // 移除事件监听
  document.removeEventListener("mousemove", handleDrag);
  document.removeEventListener("touchmove", handleDrag);
  document.removeEventListener("mouseup", endDrag);
  document.removeEventListener("touchend", endDrag);
};

// 显示箱子位置信息
const showBoxPosition = (event, index) => {
  if (isDragging.value) return;
  hoveredBoxIndex.value = index;
  updateMousePosition(event, index);
};

// 更新鼠标位置
const updateMousePosition = (event, index) => {
  if (isDragging.value || hoveredBoxIndex.value !== index) return;

  const rect = event.currentTarget.getBoundingClientRect();
  mousePosition.value = {
    x: event.clientX - rect.left + 10, // 偏移10px避免遮挡
    y: event.clientY - rect.top - 30, // 偏移-30px显示在上方
  };
};

// 隐藏箱子位置信息
const hideBoxPosition = () => {
  hoveredBoxIndex.value = -1;
};

// 切换箱子排列方式
const adjustBtn = () => {
  if (selectedBoxIndex.value === -1) {
    ElMessage.warning("请先选择一个箱子");
    return;
  }

  const selectedBox = placedBoxes.value[selectedBoxIndex.value];

  // 创建旋转后的箱子
  const rotatedBox = {
    ...selectedBox,
    boxLength: selectedBox.boxWidth,
    boxWidth: selectedBox.boxLength,
    rotated: !selectedBox.rotated,
  };

  // 临时移除当前箱子，检查旋转后是否能放在当前位置
  const otherBoxes = placedBoxes.value.filter(
    (_, index) => index !== selectedBoxIndex.value
  );

  // 检查旋转后的箱子是否超出托盘范围
  if (
    selectedBox.locationX + rotatedBox.boxLength > selectedTray.value.trayLength ||
    selectedBox.locationY + rotatedBox.boxWidth > selectedTray.value.trayWidth
  ) {
    ElMessage.warning("有箱子挡到了，切换不了排列方式");
    return;
  }

  // 检查旋转后的箱子是否与其他箱子重叠
  const testBox = { ...rotatedBox, locationX: selectedBox.x, locationY: selectedBox.y };
  let hasOverlap = false;

  for (const otherBox of otherBoxes) {
    if (isOverlapping(testBox, otherBox)) {
      hasOverlap = true;
      break;
    }
  }

  if (hasOverlap) {
    ElMessage.warning("有箱子挡到了，切换不了排列方式");
  } else {
    // 可以旋转，更新箱子
    placedBoxes.value[selectedBoxIndex.value] = rotatedBox;
  }
};

// 删除选中的箱子
const clearOneBtn = async () => {
  if (selectedBoxIndex.value === -1) {
    ElMessage.warning("请先选择一个箱子");
    return;
  }
  if (placedBoxes.value[selectedBoxIndex.value].locationId) {
    await deleteLocationListApi(placedBoxes.value[selectedBoxIndex.value].locationId);
    getLocationListApiFn();
  }

  // 删除选中的箱子
  placedBoxes.value.splice(selectedBoxIndex.value, 1);
  selectedBoxIndex.value = -1; // 清空选择
  ElMessage.success("箱子已删除");
};

// 清空所有箱子
const clearAllBtn = () => {
  if (placedBoxes.value.length === 0) {
    ElMessage.warning("托盘上没有箱子");
    return;
  }

  placedBoxes.value = [];
  selectedBoxIndex.value = -1;
  ElMessage.success("已清空所有箱子");
};

// 保存当前布局到本地存储
const saveBtn = async () => {
  if (!selectedTray.value) {
    ElMessage.warning("请先选择托盘");
    return;
  }

  if (placedBoxes.value.length === 0) {
    ElMessage.warning("托盘上没有箱子，无需保存");
    return;
  }

  let locationItemCommandList = placedBoxes.value.map((item) => {
    return {
      locationId: item.locationId || "",
      locationName: item.locationName,
      locationX: item.locationX,
      locationY: item.locationY,
      boxDirection: item.rotated ? 2 : 1,
      boxTypeName: item.boxTypeName,
      boxLength: item.boxLength,
      boxWidth: item.boxWidth,
      boxHeight: item.boxHeight,
    };
  });

  await saveLocationListApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: selectedTray.value.trayCode,
    trayId: selectedTray.value.trayId,
    locationItemCommandList,
  });
  ElMessage.success(`已保存托盘 ${selectedTray.value.trayCode} 的布局`);
};
</script>

<style lang="scss" scoped>
.app-coniner {
  padding: 20px;
  overflow: auto;
}

.tray-title {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: #fafafa;
  margin-bottom: 20px;
  border-bottom: 1px solid #797979;
  color: #000000;
  font-weight: 700;
  padding-left: 30px;
}

.tray-layout {
  display: flex;
  min-width: 100%;
  height: 650px;
  overflow: auto;

  .tray-select {
    flex: 0 0 400px;
    width: 400px;
    border: 1px solid #797979;
    position: relative;
    margin-bottom: 20px;
    margin-right: 100px;
  }
}

.drag-tip {
  margin: 10px 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 4px;
  color: #409eff;
  font-size: 14px;
  display: inline-block;
}

.container {
  flex: 0 0 1200px;
  width: 1200px;
  height: 600px;
  border: 1px solid #797979;
  position: relative;
  margin-bottom: 20px;
  padding-top: 30px;
}

.width-label {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.height-label {
  position: absolute;
  left: -80px;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  background-color: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.tray {
  position: absolute;
  bottom: 0;
  left: 0;
  border: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.scale-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.scale-line {
  position: absolute;
  background-color: #ddd;

  &.vertical {
    width: 1px;
    height: 100%;
  }

  &.horizontal {
    width: 100%;
    height: 1px;
  }
}

.box {
  position: absolute;
  border: 1px solid #333;
  background-color: rgba(255, 193, 7, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 193, 7, 1);
    transform: scale(1.02);
  }

  &.selected {
    border: 2px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.8);
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
  }

  &.dragging {
    opacity: 0.8;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 2px solid #409eff;
    background-color: rgba(64, 158, 255, 0.9);
    cursor: grabbing;
  }
}

.position-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 2000;
  transform: translateX(-50%);
}

.table-fun {
  display: flex;
  width: 100%;
  .box-type {
    width: 650px;
    flex: 0 0 800px;
    margin-right: 10px;
  }
  .kuwei {
    width: 800px;
    flex: 0 0 1000px;
  }
}
</style>
