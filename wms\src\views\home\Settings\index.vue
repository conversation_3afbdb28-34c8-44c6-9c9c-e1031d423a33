<template>
  <div class="set-page">
    <div class="set-menu" v-hasPermission="['WmsHome:settings:tray']" @click="router.push('/WmsHome/TraySet')">托盘设置</div>
    <div class="set-menu" v-hasPermission="['WmsHome:settings:mainData']" @click="router.push('/WmsHome/MainData')">主数据</div>
    <div class="set-menu" v-hasPermission="['WmsHome:settings:log']" @click="router.push('/WmsHome/LogList')">事务日志</div>
  </div>
</template>

<script setup>
const router = useRouter();
</script>

<style lang="scss" scoped>
.set-page {
  padding: 100px 100px 100px 280px;
  display: flex;
  flex-wrap: wrap;

  .set-menu {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 300px;
    height: 150px;
    border: 2px solid #aaaaaa;
    background-color: #f3f3f3;
    margin: 50px;
    border-radius: 15px;
    font-size: 25px;
    cursor: pointer;
  }
}
</style>
