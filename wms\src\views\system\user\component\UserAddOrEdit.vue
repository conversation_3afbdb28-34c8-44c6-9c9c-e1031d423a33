<!--
 * @Author: 方志良 
 * @Date: 2024-04-01 14:51:34
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-08-02 15:28:19
 * @FilePath: \biweiman-mes-wms-front-end\src\views\system\user\component\UserAddOrEdit.vue
 * 
-->
<template>
  <el-dialog v-model="isvisable" draggable :title="editObj.userId ? '编辑' : '新增'" align-center width="520px"
    @close="cancelBtn">
    <el-form ref="dialogRef" :model="dialogueForm" label-width="80px" style="padding:30px" :rules="rules">
      <el-form-item label="工号" prop="username">
        <el-input v-model="dialogueForm.username" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="姓名" prop="nickname">
        <el-input v-model="dialogueForm.nickname" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="角色" prop="roleIds">
        <el-select v-model="dialogueForm.roleIds" multiple collapse-tags placeholder="请选择" style="width: 100%">
          <el-option v-for="item in roleList" :key="item.roleId" :label="item.roleName" :value="item.roleId" />
        </el-select>
      </el-form-item>
      <el-checkbox style="margin-left:50px;" v-model="dialogueForm.related" label="是否创建员工" size="large" />
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmBtn">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { addAccountPageApi, editAccountPageApi, getRoleListApi } from '@/api/system/user'
const { proxy } = getCurrentInstance();
let props = defineProps({
  isvisable: {
    type: Boolean,
    default: false,
  },
  editObj: {
    type: Object,
    default: () => { },
  }
});

const dialogueForm = ref({ roleIds: [], related: false })
const rules = reactive({
  username: [{ required: true, message: '此处不能为空', trigger: 'blur' },],
  nickname: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
  roleIds: [{ required: true, message: '请选择', trigger: 'blur' }],
})

watchEffect(() => {
  if (props.isvisable && Object.keys(props.editObj).length > 0) {
    dialogueForm.value = JSON.parse(JSON.stringify(props.editObj));
    dialogueForm.value.roleIds = props.editObj.roleDTOList.map(item => item.roleId)
  }
});

const emit = defineEmits(['update:isvisable', 'onSuccess']);
//取消
const dialogRef = ref(null)
const cancelBtn = () => {
  dialogueForm.value = { roleIds: [], related: false }
  emit('update:isvisable', false);
  dialogRef.value.resetFields()
}

//确定
let loading = ref(false)
const confirmBtn = async () => {
  let flag = await dialogRef.value.validate()
  if (flag) {
    try {
      loading.value = true
      if (dialogueForm.value.userId) {
        await editAccountPageApi(dialogueForm.value)
        proxy.$modal.msgSuccess(`编辑成功`);
        cancelBtn()
        emit('onSuccess');
      } else {
        await addAccountPageApi(dialogueForm.value)
        proxy.$modal.msgSuccess(`新增成功`);
        cancelBtn()
        emit('onSuccess');
      }

    } finally {
      loading.value = false
    }
  }
}

//角色
let roleList = ref([])
const getRoleListApiFn = async () => {
  roleList.value = await getRoleListApi()
}


onActivated(() => {
  getRoleListApiFn()
})

onMounted(() => {
  getRoleListApiFn()
})
</script>

<style lang="scss" ></style>