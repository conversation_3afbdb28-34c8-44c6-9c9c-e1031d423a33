/*
 * @Author: 方志良 
 * @Date: 2024-04-03 15:37:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-07 10:59:30
 * @FilePath: \mewyeah-mes-front-end\src\api\system\employeeApi.js
 * 
 */
import request from '@/utils/request';

//员工分页
export const getEmployeePageApi=(params)=>{
  return request({
    url: '/system/employee/table',
    method: 'get',
    params
  })
}

//员工列表
export const getEmployeeListApi=(params)=>{
  return request({
    url: '/system/employee/list',
    method: 'get',
    params
  })
}

//员工新增
export const addEmployeePageApi=(data)=>{
  return request({
    url: '/system/employee/add',
    method: 'post',
    data
  })
}

//员工编辑
export const editEmployeePageApi=(data)=>{
  return request({
    url: '/system/employee/edit',
    method: 'put',
    data
  })
}

//员工删除
export const deleteEmployeePageApi=(data)=>{
  return request({
    url: '/system/employee/remove',
    method: 'delete',
    data
  })
}

//员工启用停用
export const adjustEmployeeStatusApi=(data)=>{
  return request({
    url: '/system/employee/switchStatus',
    method: 'put',
    data
  })
}