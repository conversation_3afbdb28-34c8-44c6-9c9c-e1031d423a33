import request from '@/utils/request';

//订单分页
export const getOrderPageApi = (params) => {
  return request({
    url: `/auto/order/table`,
    method: 'get',
    params
  });
}

//订单新增
export const addOrderPageApi = (data) => {
  return request({
    url: '/auto/order/add',
    method: 'post',
    data
  });
}

//订单编辑
export const editOrderPageApi = (data) => {
  return request({
    url: '/auto/order/update',
    method: 'put',
    data
  });
}

//订单删除
export const deleteOrderPageApi = (id) => {
  return request({
    url: `/auto/order/del/${id}`,
    method: 'delete',
  });
}

//订单发布
export const issueOrderPageApi = (id) => {
  return request({
    url: `/auto/order/issue/${id}`,
    method: 'put',
  });
}

//订单撤销发布
export const resetOrderPageApi = (id) => {
  return request({
    url: `/auto/order/unissue/${id}`,
    method: 'put',
  });
}


//项目分页
export const getProjectPageApi = (params) => {
  return request({
    url: `/auto/order/item/table`,
    method: 'get',
    params
  });
}

//项目新增
export const addProjectPageApi = (data) => {
  return request({
    url: '/auto/order/item/add',
    method: 'post',
    data
  });
}

//项目编辑
export const editProjectPageApi = (data) => {
  return request({
    url: '/auto/order/item/update',
    method: 'put',
    data
  });
}

//项目删除
export const deleteProjectPageApi = (id) => {
  return request({
    url: `/auto/order/item/del/${id}`,
    method: 'delete',
  });
}