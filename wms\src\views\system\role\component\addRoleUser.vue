<!--
 * @Author: 方志良 
 * @Date: 2024-04-03 13:44:53
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-03 14:09:24
 * @FilePath: \mewyeah-mes-front-end\src\views\system\role\component\addRoleUser.vue
 * 
-->
<template>
  <el-dialog v-model="isvisable" draggable title="新增" align-center width="520px" @close="cancelBtn">
    <el-form ref="dialogRef" :model="dialogueForm" label-width="80px" style="padding:30px" :rules="rules">
      <el-form-item label="成员" prop="ids">
        <el-select v-model="dialogueForm.ids" filterable multiple collapse-tags placeholder="请选择" style="width: 100%">
          <el-option v-for="item in userList" :key="item.userId" :label="`${item.nickname}-${item.username}`"
            :value="item.userId" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmBtn">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { listUserApi } from '@/api/system/user'
import { addUserPageApi } from '@/api/system/roleApi'
const { proxy } = getCurrentInstance();
let props = defineProps({
  isvisable: {
    type: Boolean,
    default: false,
  },

  roleId: {
    type: String,
    default: "",
  }
});

const dialogueForm = ref({ ids: [] })
const rules = reactive({
  ids: [{ required: true, message: '请选择', trigger: 'blur' }],
})

const emit = defineEmits(['update:isvisable', 'onSuccess']);
//取消
const dialogRef = ref(null)
const cancelBtn = () => {
  dialogueForm.value = { ids: [] }
  emit('update:isvisable', false);
  dialogRef.value.resetFields()
}

//确定
let loading = ref(false)
const confirmBtn = async () => {
  let flag = await dialogRef.value.validate()
  if (flag) {
    try {
      loading.value = true
      await addUserPageApi({ ...dialogueForm.value, roleId: parseInt(props.roleId) })
      proxy.$modal.msgSuccess(`新增成功`);
      cancelBtn()
      emit('onSuccess');
    } finally {
      loading.value = false
    }
  }
}

//成员
let userList = ref([])
const listUserApiFn = async () => {
  userList.value = await listUserApi({ status: 1 })
}

onMounted(() => {
  listUserApiFn()
})
</script>

<style lang="scss" ></style>