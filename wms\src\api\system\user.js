import request from '@/utils/request';

//账户分页
export const getAccountPageApi=(params)=>{
  return request({
    url: '/system/user/table',
    method: 'get',
    params
  })
}

// 账户列表
export function listUserApi(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query,
  });
}

//账户新增
export const addAccountPageApi=(data)=>{
  return request({
    url: '/system/user/add',
    method: 'post',
    data
  })
}

//账户编辑
export const editAccountPageApi=(data)=>{
  return request({
    url: '/system/user/edit',
    method: 'put',
    data
  })
}

//删除
export const deleteAccountPageApi=(data)=>{
  return request({
    url: '/system/user/remove',
    method: 'delete',
    data
  })
}

//停用
export const editSwitchStatusApi=(data)=>{
  return request({
    url: '/system/user/switchStatus',
    method: 'put',
    data
  })
}

//重置密码
export const resetPasswordApi=(data)=>{
  return request({
    url: '/system/user/resetPassword',
    method: 'put',
    data
  })
}

//角色列表
export const getRoleListApi=(params)=>{
  return request({
    url: '/system/role/list',
    method: 'get',
    params
  })
}