<!--
 * @Author: 方志良 
 * @Date: 2024-03-31 17:03:39
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-01-06 17:16:49
 * @FilePath: \biweiman-mes-wms-front-end\src\components\Search\index.vue
 * 
-->
<template>
  <el-input v-model="localValue" @keyup.enter.native="searchList" placeholder="搜索"
    style="width: 180px;margin-left: 10px;">
    <template #suffix>
      <el-icon class="el-icon--right" style="cursor:pointer ;" @click="searchList">
        <Search />
      </el-icon>
    </template>
  </el-input>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue';
let props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(['update:modelValue']);


const localValue = ref(props.modelValue);

const searchList = () => {
  emit('update:modelValue', localValue.value);
};
</script>

<style lang="scss" scoped></style>