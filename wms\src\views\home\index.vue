<template>
  <div class="home-page">
    <div class="home-menu"  v-hasPermission="['WmsHome:main:jianxuan']" @click="router.push('/WmsHome/JianXuan')">
      <h2>拣选:3</h2>
      <img src="@/assets/images/jianxuan.svg" alt="拣选" />
    </div>
    <div class="home-menu" v-hasPermission="['WmsHome:main:cunchu']" @click="router.push('/WmsHome/CunChu')">
      <h2>存储:5</h2>
      <img src="@/assets/images/cunchu.svg" alt="存储" />
    </div>
    <div class="home-menu" v-hasPermission="['WmsHome:main:pandian']" @click="router.push('/WmsHome/PanDian')">
      <h2>盘点:3</h2>
      <img src="@/assets/images/pandian.svg" alt="盘点" />
    </div>
    <div class="home-menu" v-hasPermission="['WmsHome:main:handle']" @click="router.push('/WmsHome/Handle')">
      <h2>手动操作</h2>
      <img src="@/assets/images/shoudong.svg" alt="手动操作" />
    </div>
    <div class="home-menu" v-hasPermission="['WmsHome:main:order']" @click="router.push('/WmsHome/GaiLan')">
      <h2>订单概览</h2>
      <img src="@/assets/images/gailan.svg" alt="订单概览" />
    </div>
    <div class="home-menu" v-hasPermission="['WmsHome:main:template']" @click="router.push('/WmsHome/MoBan')">
      <h2>订单模板</h2>
      <img src="@/assets/images/moban.svg" alt="订单模板" />
    </div>
  </div>
</template>

<script setup>
const router = useRouter();
</script>

<style lang="scss" scoped>
.home-page {
  padding: 100px 100px 100px 280px;
  display: flex;
  flex-wrap: wrap;

  .home-menu {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 300px;
    height: 300px;
    border: 2px solid #aaaaaa;
    background-color: #f3f3f3;
    margin: 50px;
    border-radius: 15px;
    cursor: pointer;

    h2 {
      font-weight: 700;
    }
  }
}
</style>
