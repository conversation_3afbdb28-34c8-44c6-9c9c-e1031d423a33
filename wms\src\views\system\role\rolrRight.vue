<template>
  <div class="oc right">
    <el-card class="card" style="height: calc(100vh - 104px);">
      <template v-if="props.menuId">
        <el-tabs v-model="activeName" style="margin-top: 10px;" :stretch=true @tab-click="handleClick">
          <el-tab-pane label="菜单管理" :name="0"></el-tab-pane>
          <el-tab-pane label="账号管理" :name="1"></el-tab-pane>
        </el-tabs>

        <template v-if="activeName === 0">
          <el-button type="primary" v-hasPermission="['system:menu:update']" @click="updateFunBtn">更新功能</el-button>
          <el-tree :data="deptOptions" show-checkbox style="margin-top: 20px;" :default-expand-all="false" ref="treeRef"
            node-key="menuId" :check-strictly="false" empty-text="加载中，请稍候"
            :props="{ label: 'label', children: 'children' }"></el-tree>
        </template>

        <template v-if="activeName === 1">
          <div class="action">
            <div>
              <el-button type="primary" @click="dialogFlag = true;"
                v-hasPermission="['system:employee:add']">添加成员</el-button>
              <el-button :disabled="multiple" @click="deleteTableBtn"
                v-hasPermission="['system:employee:remove']">移除成员</el-button>
            </div>
            <div class="selection">
              <SearchList v-model="searchForms.keywords" @update:modelValue="theListFn"></SearchList>
            </div>
          </div>
          <el-table :data="tableList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="60" align="center"></el-table-column>
            <el-table-column label="姓名" prop="nickname" align="center"></el-table-column>
            <el-table-column label="工号" prop="username" align="center"></el-table-column>
            <el-table-column label="状态" prop="status" align="center">
              <template #default="scope">
                <span :style="{ color: scope.row.status == 1 ? '#008ca1' : '#fc6302' }">● </span>
                <span>{{ proxy.getDictLbelFn('sys_status', scope.row.status) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </template>
    </el-card>


    <addRoleUser v-model:isvisable="dialogFlag" :roleId="props.menuId" @onSuccess="theListFn"></addRoleUser>
  </div>
</template>

<script setup>
import { getMenuSelectTreeByRole } from '@/api/system/menuApi'
import { getUserPageApi, updateolePermissionApi, deleteUserPageApi } from '@/api/system/roleApi'
const addRoleUser = defineAsyncComponent(() => import('./component/addRoleUser'))
import { nextTick } from 'vue';
const { proxy } = getCurrentInstance();
let props = defineProps({
  menuId: {
    type: String,
    default: "",
  }
});

//切换
const activeName = ref(0)
const handleClick = (e) => {
  nextTick(() => {
    if (activeName.value === 0) {
      listMenuFn(props.menuId)
    }
  })
}


const { menuId } = toRefs(props);
watch(menuId, (newVal) => {
  if (newVal) {
    listMenuFn(newVal)
    theListFn()
  }
})
//左边角色
const deptOptions = ref([])
const treeRef = ref(null);
const listMenuFn = async (id) => {
  let res = await getMenuSelectTreeByRole(id)
  deptOptions.value = res.menus
  //默认选中
  //setChecked三个参数1. 要选中的节点的 key 或者数据2. 一个布尔类型参数表明是否选中. 3. 一个布尔类型参数表明是否递归选中子节点
  nextTick(() => {
    if (treeRef.value) {
      res.checkedKeys.forEach(item => {
        treeRef.value.setChecked(item, true, false)
      })
    }
  })
}
const updateFunBtn = async () => {
  //拿到选中的数据
  const checkedNodesData = treeRef.value.getCheckedNodes(false, true);
  let arr = checkedNodesData.map(item => item.menuId)
  let obj = {
    roleId: parseInt(props.menuId),
    ids: arr
  }
  await updateolePermissionApi(obj)
  proxy.$modal.msgSuccess(`更新成功`);
}

//右边列表分页
const tableList = ref([])
const searchForms = ref({
  keywords: "",
})
const theListFn = async () => {
  let obj = {
    keywords: searchForms.value.keywords,
    roleId: props.menuId
  }
  tableList.value = await getUserPageApi(obj)
}

//删除
const multiple = ref(true);
const deleteIds = ref([])
const handleSelectionChange = (val) => {
  deleteIds.value = val.map(item => item.userId)
  multiple.value = val.length ? false : true
}
const deleteTableBtn = () => {
  proxy.$modal
    .confirm(`是否确认删除选中的的数据项？`)
    .then(() => deleteUserPageApi({ ids: deleteIds.value, roleId: props.menuId }))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      theListFn()
    })
    .catch(() => {
    });
}

//弹框
let dialogFlag = ref(false)



onMounted(() => {

})

</script>

<style lang="scss" scoped></style>