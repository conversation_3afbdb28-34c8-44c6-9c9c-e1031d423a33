<template>
  <div>
    <el-dialog title="项目" v-model="dialogueFlag" width="1400px" append-to-body>
      <div class="oc" style="background-color: #fff; padding: 30px">
        <div class="action">
          <div>
            <el-button
              type="primary"
              @click="handleAddEdit({})"
              v-if="rowObj.orderStatusValue == 1"
            >
              添加
            </el-button>
          </div>
          <div class="selection">
            <SearchList
              v-model="searchForms.keywords"
              @update:modelValue="theResetPageFn"
            ></SearchList>
          </div>
        </div>

        <el-table :data="tableList">
          <el-table-column
            label="物料编码"
            prop="materialCode"
            align="center"
          ></el-table-column>

          <el-table-column label="数量" prop="qty" align="center"></el-table-column>

          <el-table-column
            label="操作"
            width="200"
            align="center"
            v-if="rowObj.orderStatusValue == 1"
          >
            <template #default="scope">
              <div class="div-flex">
                <el-button class="table-btn" text @click="handleAddEdit(scope.row)">
                  编辑
                </el-button>
                <el-button class="table-btn" text @click="deleteBtn(scope.row)">
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-row justify="end" style="margin-top: 10px">
          <el-pagination
            v-show="thePage.total > 0"
            background
            :total="thePage.total"
            v-model:current-page="thePage.current"
            v-model:page-size="thePage.size"
            @current-change="thePageFn"
            layout="total,prev, pager, next"
          ></el-pagination>
        </el-row>
      </div>
    </el-dialog>
    <ProjectAddEdit ref="addEdieModalRef" @on-success="theResetPageFn"></ProjectAddEdit>
  </div>
</template>

<script setup>
import { getProjectPageApi, deleteProjectPageApi } from "@/api/home/<USER>";
const ProjectAddEdit = defineAsyncComponent(() => import("./Modal/ProjectAddEdit.vue"));
const { proxy } = getCurrentInstance();

let dialogueFlag = ref(false);

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0,
});
let tableList = ref([]);
const thePageFn = async () => {
  let obj = {
    orderId: rowObj.value.orderId,
  };
  let res = await getProjectPageApi(obj);
  // tableList.value = [res];
  console.log(tableList.value, " tableList.value");
  tableList.value = res.rows;
  thePage.value.total = res.total;
};

//搜索
const searchForms = ref({
  keywords: "",
});
const theResetPageFn = () => {
  thePage.value.current = 1;
  thePageFn();
};

//编辑
const addEdieModalRef = ref(null);
const handleAddEdit = (row) => {
  addEdieModalRef.value.openModal(row, rowObj.value);
};

// 初始化弹窗
let rowObj = ref({});
function openModal(val) {
  rowObj.value = val;
  dialogueFlag.value = true;
  thePageFn();
}

//删除
const deleteBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认删除此数据项？`)
    .then(() => deleteProjectPageApi(row.orderItemId))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn();
    })
    .catch(() => {});
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
