<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 13:32:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 16:22:41
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\TrayPage\modal\ListMOdal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.orderItemId !== undefined ? '编辑项目' : '新增项目'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
          <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="addForm.materialCode" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="数量" prop="qty">
            <el-input-number
              v-model="addForm.qty"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading">
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { addProjectPageApi, editProjectPageApi } from "@/api/home/<USER>";

import { useStore } from "vuex";
const store = useStore();

const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  materialCode: [{ required: true, message: "请输入", trigger: "blur" }],
  qty: [{ required: true, message: "请输入", trigger: "blur" }],
});

// 初始化弹窗
let rowObj = ref({});
function openModal(val, row) {
  rowObj.value = row;
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      if (addForm.value.orderItemId) {
        requestObj = editProjectPageApi({
          ...addForm.value,
          cabinetId: rowObj.value.cabinetId,
          orderId: rowObj.value.orderId,
        });
      } else {
        requestObj = addProjectPageApi({
          ...addForm.value,
          cabinetId: rowObj.value.cabinetId,
          orderId: rowObj.value.orderId,
        });
      }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
