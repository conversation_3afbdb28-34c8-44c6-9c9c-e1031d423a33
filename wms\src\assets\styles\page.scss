@import './variables.module.scss';

//搜索+table
.oc {
  width: 100%;
  background-color: #f5f7f9;
  padding: 10px;

 //配置标题
 .title{
   background-color: white;
   height: 70px;
    margin-bottom: 10px;
   display: flex;
   flex-direction: row;
   justify-content: space-between;
   align-items: center;
   box-shadow:0px 0px 12px rgba(0, 0, 0, 0.12);

   button {
    margin-left: 10px !important;
    margin-right: 10px !important;
  }
   &-label {
    font-size: 20px;
    margin-right: 10px;
    font-weight: bold;
  }
 }

 .single {
  margin: 10px;
  min-height: calc(100vh - 250px);
  padding: 10px 20px 0 20px;
  &-title {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 20px;
    label {
      font-size: 20px;
      font-weight: bold;
      margin-right: 20px;
    }
  }
}

 .config-col{
  margin-top: 15px;
  margin-bottom: 15px;
 }
 .config-label{
  display: inline-block;
  margin-right: 5px;
 }
 .div-flex{
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
 }

 .table-btn{
  color: #008ca1 !important;
  margin: 5px !important;
  margin-left: 5px !important;
  padding: 1px;
}
  
  .card {
    min-height: calc(100vh - 84px);
    width: 100%;
    overflow: auto;

    ::-webkit-scrollbar {
      /*隐藏滚轮*/
      display: none;
    }

  }
  .action {
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .selection {
      display: flex;
    }

    label {
      text-align: right;
      line-height: 32px;
      vertical-align: middle;
      margin-right: 10px;
      margin-left: 10px;
      font-size: 16px;
      font-weight: bold;
    }

    &-select {
      margin-right: 10px;
      width: 180px;
    }

    &-input {
      width: 180px;
    }
  }



 .el-pager .is-active{
    background-color: #008ca1 !important;
  }
}


//左右布局
.tc{
  width: 100%;
  display: flex;
  padding: 10px;
  flex-direction: row;
  .left{
    margin-right: 10px;
    width:25%;
    min-height: calc(100vh - 84px);
    &-btn {
      margin-top: 20px;
			margin-bottom: 20px;
      color: #ffffff;
      width: 100%;
		}
    .menu{
      display: flex;
			justify-content: space-between;
			align-items: center;
    
      .btn-group{
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &-btn {
				color: #008ca1;
        height: 32px;
        padding: 5px;
        margin-left: 0;
			}
    }

    .el-menu-item.is-active{
      color: #006699 !important;
      background-color: #c3d5d9 !important;
    }
  }
  .right {
    min-height: calc(100vh - 84px);
		width: calc(75%);
		overflow: auto;
  }
}


//elementPlus多选框颜色
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #006699 !important;
  border-color:#006699 !important;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
  background-color: #006699 !important;
  border-color:#006699 !important;
}

//elementPlus弹框border
.el-dialog__body {
  padding: 10px 0 0 !important;
  border-top: 1px solid #ebebeb !important;
  border-bottom: 1px solid #ebebeb !important;
}


//elementPlus单选框颜色
.el-radio__input.is-checked .el-radio__inner {
  border-color: #006699 !important;
  background-color: #006699 !important;
}
.el-radio__input.is-checked+.el-radio__label {
 color: #606266 !important;
}


//tab栏
.el-tabs{
  width: 50%;
  margin: 0 auto ;
}
.el-tabs__item{
  font-size: 18px !important;
}
.el-tabs__item.is-active{
  color: #006699 !important;
}
.el-tabs__active-bar{
  background-color: #006699 !important;
}

//鼠标选中的颜色
.el-input__inner::selection {
  background: rgba(54, 98, 236, 0.2);
}


.text-over{
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.h-set{
  font-weight: 900;
}








