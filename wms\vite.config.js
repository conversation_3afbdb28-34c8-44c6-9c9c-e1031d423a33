/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 10:52:27
 * @FilePath: \aosikai-cabinet-wms-web\base\vite.config.js
 * 
 */
import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import createVitePlugins from './vite/plugins';
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV } = env;
  return {
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    build: {
      outDir: '奥斯凯-base'
    },
    plugins: createVitePlugins(env, command === 'build'),
    lintOnSave: process.env.NODE_ENV !== 'production',
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        // 设置路径别名
        '@': path.resolve(__dirname, './src')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 80,
      host: '127.0.0.1',
      open: false,
      proxy: {
        '/prod-api': {
          // target: "http://biweiman.pz-smart.cn/prod-api", 
          target: 'http://*************:8000',
          changeOrigin: true,
          rewrite: p => p.replace(/\/prod-api/, '')
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    }
  };
});
